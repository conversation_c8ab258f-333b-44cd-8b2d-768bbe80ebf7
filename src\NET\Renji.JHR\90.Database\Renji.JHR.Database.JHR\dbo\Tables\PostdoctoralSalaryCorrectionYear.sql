﻿CREATE TABLE [dbo].[PostdoctoralSalaryCorrectionYear]
(
	[ID] UNIQUEIDENTIFIER NOT NULL , 
    [EmployeeId]   UNIQUEIDENTIFIER NOT NULL,
    [SalaryId] UNIQUEIDENTIFIER NOT NULL,
    [StartTime]     DATETIME NULL,
    [EndTime]     DATETIME NULL,
    [Remark] NVARCHAR(300) NULL,
    [OldYearlySalary] DECIMAL(18, 4) NULL,
    [NewYearlySalary] DECIMAL(18, 4) NULL,
    [OldMonthSalary] DECIMAL(18, 4) NULL,
    [NewMonthSalary] DECIMAL(18, 4) NULL,
    [SuccessfulProject] NVARCHAR(200) NULL,
    [Adjust] NVARCHAR(200) NULL,
    [OldStationId] UNIQUEIDENTIFIER NULL,
    [OldStationWage] DECIMAL(18, 4) NULL,
    [NewStationId] UNIQUEIDENTIFIER NULL,
    [NewStationWage] DECIMAL(18, 4) NULL,
    [BackPayMonth] DECIMAL(18, 4) NULL,
    [EnumPaymentType] int NOT NULL default(2),
    [BackPayDeduction] DECIMAL(18, 4) NULL,
    [ActualBackPayDeduction] DECIMAL(18, 4) NULL,
    
    [SocialSecurityBase]          DECIMAL(18, 4) NULL,
    [HousingFundBase]            DECIMAL(18, 4) NULL,
    [PensionInsurance] DECIMAL(18, 4) NULL,
    [BackDeductionPensionInsurance] DECIMAL(18, 4) NULL,
    [MedicalInsurance] DECIMAL(18, 4) NULL,
    [BackDeductionMedicalInsurance] DECIMAL(18, 4) NULL,
    [UnemploymentInsurance] DECIMAL(18, 4) NULL,
    [BackDeductionUnemploymentInsurance] DECIMAL(18, 4) NULL,
    [HousingFund] DECIMAL(18, 4) NULL,
    [BackDeductionHousingFund] DECIMAL(18, 4) NULL,
    [SupplementaryHousingFund] DECIMAL(18, 4) NULL,
    [BackDeductionSupplementaryHousingFund] DECIMAL(18, 4) NULL,
    [OldMembershipFee] DECIMAL(18, 4) NULL,
    [NewMembershipFee] DECIMAL(18, 4) NULL,
    [CalculatedMembershipFee] DECIMAL(18, 4) NULL,
    [ActualMembershipFee] DECIMAL(18, 4) NULL,
    [MembershipFee] DECIMAL(18, 4) NULL,
    [BackDeductionMembershipFee] DECIMAL(18, 4) NULL,

	[Deleted]		BIT                 NOT NULL,
    [Creator]		NVARCHAR(50)        NULL, 
    [CreateTime]	DATETIME            NULL,
    [LastEditor]	NVARCHAR(50)        NULL, 
    [LastEditTime]	DATETIME            NULL, 
    CONSTRAINT [PK_PostdoctoralSalaryCorrectionYear] PRIMARY KEY CLUSTERED ([ID] ASC),
    CONSTRAINT [FK_PostdoctoralSalaryCorrectionYear_Employee] FOREIGN KEY ([EmployeeId]) REFERENCES [dbo].[Employee] ([ID]),
    CONSTRAINT [FK_PostdoctoralSalaryCorrectionYear_Salary] FOREIGN KEY ([SalaryId]) REFERENCES [dbo].[Salary] ([ID]),
    CONSTRAINT [FK_PostdoctoralSalaryCorrectionYear_Station] FOREIGN KEY ([NewStationId]) REFERENCES [dbo].[Station] ([ID]),
)
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'博士后修正年薪',
    @level0type = N'SCHEMA',
    @level0name = N'dbo', 
    @level1type = N'TABLE', 
    @level1name = N'PostdoctoralSalaryCorrectionYear';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'备注',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'Remark';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'调整为',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'Adjust';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补发/补扣',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'BackPayDeduction';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'新年薪',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'NewYearlySalary'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'新岗位工资',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = 'NewStationWage'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'中选项目',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'SuccessfulProject'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补发月份数',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'BackPayMonth'
 GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'养老保险',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'PensionInsurance';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补扣养老保险',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = 'BackDeductionPensionInsurance';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'医疗保险',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'MedicalInsurance';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补扣医疗保险',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = 'BackDeductionMedicalInsurance';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'失业保险',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'UnemploymentInsurance';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补扣失业保险',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = 'BackDeductionUnemploymentInsurance';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'公积金',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'HousingFund';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补扣公积金',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = 'BackDeductionHousingFund';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补充公积金',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'SupplementaryHousingFund';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补扣补充公积金',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = 'BackDeductionSupplementaryHousingFund';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'原会费金额',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'OldMembershipFee';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'新会费金额',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'NewMembershipFee';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'计算会费金额',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'CalculatedMembershipFee';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'实际会费金额',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'ActualMembershipFee';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'会费',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'MembershipFee';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补扣会费',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = 'BackDeductionMembershipFee';
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'社保基数',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'SocialSecurityBase'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'公积金基数',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'HousingFundBase'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'新岗位ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'NewStationId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'原岗位ID',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'OldStationId'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'原岗位工资',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'OldStationWage'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'原年薪',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'OldYearlySalary'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'原月薪',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'OldMonthSalary'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'新月薪',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'NewMonthSalary'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'开始时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'StartTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'结束时间',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'EndTime'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'实际补发/补扣',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'ActualBackPayDeduction'
GO
EXEC sp_addextendedproperty @name = N'MS_Description',
    @value = N'补发补扣类型',
    @level0type = N'SCHEMA',
    @level0name = N'dbo',
    @level1type = N'TABLE',
    @level1name = N'PostdoctoralSalaryCorrectionYear',
    @level2type = N'COLUMN',
    @level2name = N'EnumPaymentType';
GO