<template>
  <div class="app-container">
    <layout4>
      <template #main>
        <el-form v-if="!employeeId" ref="ref_searchFrom" :inline="true" :model="listQuery">
          <el-form-item>
            <el-input v-model="listQuery.uid" clearable placeholder="唯一码" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.empCode" clearable placeholder="工号" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.displayName" clearable placeholder="姓名" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.deptName" clearable placeholder="部门" />
          </el-form-item>
          <el-form-item>
            <el-input v-model="listQuery.officialRankName" clearable placeholder="职别" />
          </el-form-item>
          <el-form-item>
            <el-select v-model="listQuery.hireStyleId" clearable placeholder="请选择在职方式">
              <el-option v-for="item in hireStyleOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
            <el-button v-if="salaryData.enumStatus == 1" type="primary" icon="el-icon-plus" @click="showDialog()">新增</el-button>
            <el-button v-if="salaryData.enumStatus == 1" type="primary" @click="downloadexceltemplate">模板下载</el-button>
            <el-upload v-if="salaryData.enumStatus == 1" action="" style="float:right;margin-left:10px;" :http-request="importExcel" accept=".xlsx" :show-file-list="false">
              <el-button slot="trigger" icon="el-icon-upload" type="primary">导入</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
        <el-table ref="tableList" v-loading="listLoading" :data="pageList" border stripe fit highlight-current-row style="width: 100%;" :header-cell-style="{ background: '#F5F7FA', color: '#606266' }" @sort-change="sortChange">
          <el-table-column label="部门" prop="Employee.Department.Name" sortable="custom">
            <template slot-scope="{ row }">
              <span>{{ row.deptName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="职别" prop="Employee.EmployeeHR.OfficialRank.Name" sortable="custom">
            <template slot-scope="{ row }">
              <span>{{ row.officialRankName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="在职方式" prop="Employee.EmployeeHR.HireStyle.Name" sortable="custom" :min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.hireStyleName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="原岗位等级" prop="OldStation.Name" sortable="custom" :min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.stationLevel }}</span>
            </template>
          </el-table-column>
          <el-table-column label="原岗位工资" prop="OldStationWage" sortable="custom" :min-width="100" header-align="left" align="right">
            <template slot-scope="{ row }">
              <span>{{ row.oldStationWage | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="原岗位津贴" prop="OldStationAllowance" :min-width="100" header-align="left" align="right">
            <template slot-scope="{ row }">
              <span>{{ row.oldStationAllowance | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="原会费" prop="OldMembershipFee" :min-width="100" header-align="left" align="right">
            <template slot-scope="{ row }">
              <span>{{ row.oldMembershipFee | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <!--
          <el-table-column label="调整为" prop="Adjust" sortable="custom" :min-width="100">
            <template slot-scope="{ row }">
              <span>{{ row.adjust }}</span>
            </template>
          </el-table-column>
          -->
          <el-table-column label="新岗位等级" prop="NewStation.Name" sortable="custom" :min-width="130">
            <template slot-scope="{ row }">
              <span>{{ row.newStationName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="新岗位工资" prop="NewStationWage" sortable="custom" :min-width="130" header-align="left" align="right">
            <template slot-scope="{ row }">
              <span>{{ row.newStationWage | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="新岗位津贴" prop="NewStationAllowance" sortable="custom" :min-width="130" header-align="left" align="right">
            <template slot-scope="{ row }">
              <span>{{ row.newStationAllowance | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="新会费" prop="NewMembershipFee" :min-width="100" header-align="left" align="right">
            <template slot-scope="{ row }">
              <span>{{ row.newMembershipFee | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="差额" prop="Difference" sortable="custom" header-align="left" align="right">
            <template slot-scope="{ row }">
              <span>{{ row.difference | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补发/补扣类型" sortable="custom" :min-width="120" prop="EnumPaymentType">
            <template slot-scope="{ row }">
              <span>{{ row.enumPaymentTypeDesc }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补发/补扣月份数" prop="BackPayMonth" sortable="custom" :min-width="140" header-align="left" align="right">
            <template slot-scope="{ row }">
              <span>{{ row.backPayMonth }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实际补发/补扣" prop="ActualBackPayDeduction" sortable="custom" :min-width="140" header-align="left" align="right">
            <template slot-scope="{ row }">
              <span>{{ row.actualBackPayDeduction | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实际会费" prop="ActualMembershipFee" sortable="custom" :min-width="120" header-align="left" align="right">
            <template slot-scope="{ row }">
              <span>{{ row.actualMembershipFee | formatMoney2 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" min-width="150px" prop="Remark" sortable="custom">
            <template slot-scope="{ row } ">
              <span>{{ row.remark }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="left" header-align="center" width="150" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <el-button v-if="salaryData.enumStatus == 1" type="primary" style="margin-left:5px !important; padding-left: 5px !important;" size="mini" @click="showDialog(row)">
                编辑
              </el-button>
              <el-button v-if="salaryData.enumStatus == 1" style="padding-left: 5px !important;" size="mini" type="danger" @click="deleteEmployeeSalaryCorrectionMonth(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
          <employeeTableColumns />
        </el-table>
        <c-pagination v-show="listQuery.total > 0" :total="listQuery.total" :page-sizes="[10, 20, 50]" :page.sync="listQuery.pageIndex" :limit.sync="listQuery.pageSize" @pagination="getPageList" />
      </template>
    </layout4>

    <editDialog ref="editDialog" @refreshData="getPageList" />
  </div>
</template>

<script>
import hRManageApi from '@/api/hRManage'
import salaryApi from '@/api/salary'
import editDialog from './components/editPage'
import employeeTableColumns from '@/views/salary/monthSalary/components/hrSalary/employeeTableColumns'

export default {
  components: {
    editDialog,
    employeeTableColumns
  },
  props: {
    employeeId: {
      type: String,
      default: '',
      required: false
    }
  },
  data() {
    return {
      salaryId: '',
      pageList: [],
      listQuery: {
        total: 1,
        pageIndex: 1,
        pageSize: 10,
        employeeId: ''
      },
      listLoading: false,
      hireStyleOptions: [],
      salaryData: {}
    }
  },
  methods: {
    init() {
      this.salaryId = this.$route.query.salaryId
      if (this.salaryId) {
        this.getSalary()
        if (this.employeeId) {
          this.listQuery.employeeId = this.employeeId
        }
      } else {
        this.$notice.message('系统错误，请刷新页面重试或联系管理员', 'error')
      }
      this.loadEmployeeHireStyle()
      this.getPageList()
    },
    getSalary() {
      salaryApi.getSalary({ id: this.salaryId }).then(result => {
        if (result.succeed) {
          this.salaryData = result.data
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      })
    },
    sortChange(c) {
      if (c.order === 'ascending') {
        this.listQuery.order = c.prop + ' ' + 'asc'
      } else if (c.order === 'descending') {
        this.listQuery.order = c.prop + ' ' + 'desc'
      } else {
        this.$delete(this.listQuery, 'order')
      }
      this.search()
    },
    search() {
      this.listQuery.pageIndex = 1
      this.getPageList()
    },
    getPageList() {
      this.listLoading = true
      this.listQuery.salaryId = this.salaryId
      salaryApi.queryEmployeeSalaryCorrectionMonth(this.listQuery).then(result => {
        if (result.succeed) {
          this.pageList = result.data.datas
          this.listQuery.total = result.data.recordCount
          this.listQuery.pageIndex = result.data.pageIndex
        } else {
          this.$notice.resultTip(result)
        }
      }).catch(error => {
        console.log(error)
      }).finally(() => {
        this.listLoading = false
      })
    },
    loadEmployeeHireStyle() {
      hRManageApi.queryHireStyle().then(result => {
        this.hireStyleOptions = result.data.datas
      }).catch(error => {
        console.log(error)
      })
    },
    deleteEmployeeSalaryCorrectionMonth(data) {
      this.$confirm('确定删除这条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        salaryApi.deleteEmployeeSalaryCorrectionMonth(data).then(result => {
          if (result.succeed) {
            this.getPageList()
            this.$notice.message('删除成功', 'success')
          } else {
            if (!result.succeed) {
              this.$notice.message('删除失败，请联系管理员', 'info')
            }
          }
        })
          .catch(error => {
            this.listLoading = false
            console.log(error)
          })
      }).catch(error => {
        console.log(error)
        this.listLoading = false
      })
    },
    showDialog(row) {
      this.$refs.editDialog.salaryId = this.salaryId
      this.$refs.editDialog.initDialog(row)
    },
    downloadexceltemplate() {
      hRManageApi.downlodaImportExcelTemplate({ type: 'importEmployeeSalaryCorrectionMonth' }).then(res => {
        const fileDownload = require('js-file-download')
        var filename = 'EmployeeSalaryCorrectionMonthTemplate.xlsx'
        if (res.data) {
          fileDownload(res.data, filename)
        } else {
          fileDownload(res, filename)
        }
      }).catch(() => { })
    },
    // 导入
    importExcel(params) {
      const file = params.file
      const formData = new FormData()
      salaryApi.importEmployeeSalaryCorrectionMonth(file, formData, this.salaryId).then(res => {
        if (res.succeed) {
          this.$message({ message: '导入成功', type: 'success' })
          this.getPageList()
        }
      }).catch(() => {
        this.getPageList()
      })
    }
  }
}
</script>

<style></style>
